/*
 * Copyright (C) 2008-2012 TrinityCore <http://www.trinitycore.org/>
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

#include "ChallengeMgr.h"
#include "GameTables.h"
#include "ScenarioMgr.h"
#include "ScriptMgr.h"

/// 206150 - Challenger's Might - 挑战者之力光环脚本类
// 功能作用：实现挑战模式中怪物的词缀效果系统，根据挑战等级动态调整怪物属性和行为
// 设计原理：通过光环脚本系统统一管理所有挑战模式词缀，使用效果索引区分不同词缀类型
// 技术优势：集中化管理、动态计算、实时响应挑战等级变化，支持词缀禁用机制
// 语义含义：代表挑战模式中怪物获得的强化能力，体现挑战难度的核心机制
// 底层机制：基于AuraScript光环脚本框架，通过多个效果索引实现不同词缀的独立控制
class spell_challengers_might : public AuraScript
{
    PrepareAuraScript(spell_challengers_might); // 光环脚本准备宏 - 初始化光环脚本基础结构和虚函数表

    uint8 volcanicTimer = 0;        // 火山词缀计时器 - 控制火山地刺生成的时间间隔（每8秒触发一次）
    uint32 felExplosivesTimer = 0;  // 爆裂词缀计时器 - 控制邪能炸弹生成的随机时间间隔（8-16秒）
    uint32 necroticProcDelay = 0;   // 死疽词缀触发延迟 - 防止死疽效果过于频繁触发的冷却时间（1秒）

    // 计算光环效果数值 - 根据挑战等级和词缀类型动态计算各种效果的强度
    // 参数说明：aurEff-光环效果对象，amount-效果数值（引用传递），canBeRecalculated-是否可重新计算
    void CalculateAmount(AuraEffect const* aurEff, float& amount, bool& /*canBeRecalculated*/)
    {
        auto caster = GetCaster(); // 获取光环施法者 - 通常是应用此光环的怪物单位

        // 获取当前副本的场景进度信息 - 挑战模式基于场景系统实现
        Scenario* progress = sScenarioMgr->GetScenario(caster->GetInstanceId());
        if (!progress) // 如果无法获取场景信息则退出 - 说明不在挑战模式副本中
            return;

        // 获取挑战模式数据对象 - 包含当前挑战的等级、词缀、进度等信息
        auto const& challenge = progress->GetChallenge();
        if (!challenge) // 如果无法获取挑战数据则退出 - 说明当前场景不是挑战模式
            return;

        // 获取当前挑战等级 - 决定怪物属性缩放的基础数值（通常为2-15级）
        uint32 challengeLevel = challenge->GetChallengeLevel();
        // 从游戏数据表中获取对应等级的生命值缩放系数 - 基于挑战等级的预设数值表
        GtChallengeModeHealthEntry const* gtHealth = sChallengeModeHealthTable.GetRow(challengeLevel);
        // 从游戏数据表中获取对应等级的伤害缩放系数 - 基于挑战等级的预设数值表
        GtChallengeModeDamageEntry const* gtDamage = sChallengeModeDamageTable.GetRow(challengeLevel);
        if (!gtHealth || !gtDamage) // 如果无法获取缩放数据则退出 - 防止空指针访问
            return;

        // 获取基础生命值缩放系数 - 来自游戏数据表的标量值（如2级=1.1，3级=1.2等）
        float modHealth = gtHealth->Scalar;
        // 获取基础伤害缩放系数 - 来自游戏数据表的标量值（如2级=1.1，3级=1.2等）
        float modDamage = gtDamage->Scalar;

        // 判断当前怪物是否为地下城首领 - 首领和小怪受不同词缀影响
        bool isDungeonBoss = false;
        auto creature = caster->ToCreature(); // 将Unit指针转换为Creature指针以访问怪物特有方法
        if (creature->IsDungeonBoss()) // 检查怪物模板中的首领标志位（CREATURE_FLAG_EXTRA_DUNGEON_BOSS）
            isDungeonBoss = true;

        // 根据怪物类型和激活的词缀应用额外的属性修正
        if (isDungeonBoss) // 如果是地下城首领
        {
            if (challenge->HasAffix(Affixes::Tyrannical)) // 如果激活了暴君词缀（词缀ID=9）
            {
                modHealth *= 1.4f; // 首领生命值额外增加40% - 暴君词缀对首领的强化效果
                modDamage *= 1.15f; // 首领伤害额外增加15% - 暴君词缀对首领的强化效果
            }
        }
        else if (challenge->HasAffix(Affixes::Fortified)) // 如果是普通怪物且激活了强韧词缀（词缀ID=10）
        {
            modHealth *= 1.2f; // 普通怪物生命值额外增加20% - 强韧词缀对小怪的强化效果
            modDamage *= 1.3f; // 普通怪物伤害额外增加30% - 强韧词缀对小怪的强化效果
        }

        // 将缩放系数转换为百分比数值 - 光环系统使用百分比表示属性修正
        modHealth = (modHealth - 1.0f) * 100.0f; // 如1.4倍转换为40%增益
        modDamage = (modDamage - 1.0f) * 100.0f; // 如1.3倍转换为30%增益

        // 根据光环效果索引设置对应的数值 - 每个索引对应不同的词缀或属性修正
        switch (aurEff->GetEffIndex())
        {
            case EFFECT_0: // 效果索引0 - 生命值百分比增加（SPELL_AURA_MOD_INCREASE_HEALTH_PERCENT）
                amount = modHealth; // 设置生命值增加百分比
                break;
            case EFFECT_1: // 效果索引1 - 伤害百分比增加（SPELL_AURA_MOD_DAMAGE_PERCENT_DONE）
                amount = modDamage; // 设置伤害增加百分比
                break;
            case EFFECT_2: // 效果索引2 - 狂怒词缀标记（SPELL_AURA_DUMMY）
                if (creature->IsAffixDisabled(Affixes::Raging)) // 检查狂怒词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                    // 狂怒词缀只对非首领怪物生效 - 首领不受狂怒词缀影响
                    amount = (challenge->HasAffix(Affixes::Raging) && !isDungeonBoss) ? 1 : 0;
                break;
            case EFFECT_3: // 效果索引3 - 鼓舞词缀标记（SPELL_AURA_DUMMY）
                if (creature->IsAffixDisabled(Affixes::Bolstering)) // 检查鼓舞词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                    // 鼓舞词缀对所有怪物生效 - 怪物死亡时为附近怪物提供增益
                    amount = challenge->HasAffix(Affixes::Bolstering) ? 1 : 0;
                break;
            case EFFECT_4: // 效果索引4 - 暴君词缀标记（SPELL_AURA_DUMMY）
                if (creature->IsAffixDisabled(Affixes::Tyrannical)) // 检查暴君词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                    // 暴君词缀只对首领生效 - 增强首领的属性和能力
                    amount = challenge->HasAffix(Affixes::Tyrannical) && isDungeonBoss ? 1 : 0;
                break;
            case EFFECT_7: // 效果索引7 - 火山词缀标记（SPELL_AURA_PERIODIC_DUMMY）
                if (creature->IsAffixDisabled(Affixes::Volcanic)) // 检查火山词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                    // 火山词缀对所有怪物生效 - 定期在玩家脚下生成火山地刺
                    amount = challenge->HasAffix(Affixes::Volcanic) ? 1 : 0;
                break;
            case EFFECT_8: // 效果索引8 - 死疽词缀标记（SPELL_AURA_DUMMY）
                if (creature->IsAffixDisabled(Affixes::Necrotic)) // 检查死疽词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                    // 死疽词缀对所有怪物生效 - 攻击时叠加治疗吸收护盾
                    amount = challenge->HasAffix(Affixes::Necrotic) ? 1 : 0;
                break;
            case EFFECT_9: // 效果索引9 - 强韧词缀标记（SPELL_AURA_DUMMY）
                if (creature->IsAffixDisabled(Affixes::Fortified)) // 检查强韧词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                    // 强韧词缀只对非首领怪物生效 - 增强普通怪物的属性
                    amount = challenge->HasAffix(Affixes::Fortified) && !isDungeonBoss ? 1 : 0;
                break;
            case EFFECT_10: // 效果索引10 - 血池词缀标记（SPELL_AURA_DUMMY）
                if (creature->IsAffixDisabled(Affixes::Sanguine)) // 检查血池词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                    // 血池词缀对所有怪物生效 - 死亡时在地面留下治疗血池
                    amount = challenge->HasAffix(Affixes::Sanguine) ? 1 : 0;
                break;
            case EFFECT_11: // 效果索引11 - 震荡词缀标记（SPELL_AURA_DUMMY）
                if (creature->IsAffixDisabled(Affixes::Quaking)) // 检查震荡词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                    // 震荡词缀对所有怪物生效 - 定期对玩家造成震荡伤害
                    amount = challenge->HasAffix(Affixes::Quaking) ? 1 : 0;
                break;
            case EFFECT_12: // 效果索引12 - 爆裂词缀标记（SPELL_AURA_DUMMY）
                if (creature->IsAffixDisabled(Affixes::FelExplosives)) // 检查爆裂词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                {
                    // 爆裂词缀对所有怪物生效 - 定期生成邪能炸弹
                    amount = challenge->HasAffix(Affixes::FelExplosives) ? 1 : 0;
                    // 初始化爆裂词缀计时器 - 随机2-10秒后开始生成邪能炸弹
                    felExplosivesTimer = urandms(2, 10);
                }
                break;
            case EFFECT_13: // 效果索引13 - 爆炸词缀标记（SPELL_AURA_DUMMY）
                if (creature->IsAffixDisabled(Affixes::Bursting)) // 检查爆炸词缀是否被禁用
                    amount = 0; // 如果被禁用则设置为0（不激活）
                else
                    // 爆炸词缀对所有怪物生效 - 死亡时对玩家造成爆炸伤害
                    amount = challenge->HasAffix(Affixes::Bursting) ? 1 : 0;
                break;
            default: // 默认情况 - 未定义的效果索引
                break;
        }
    } // CalculateAmount函数结束

    // Volcanic - 火山词缀周期性触发函数
    // 每秒调用一次，每8秒在玩家脚下生成火山地刺
    void OnTick(AuraEffect const* aurEff)
    {
        // 检查火山词缀是否激活且施法者在战斗中 - 只有战斗中才触发火山效果
        if (!aurEff->GetAmount() || !GetCaster()->isInCombat())
            return;

        // 火山词缀计时器控制 - 每8秒触发一次（计数器0-7）
        if (volcanicTimer == 7) // 达到7秒时重置计时器并执行火山效果
            volcanicTimer = 0;
        else
        {
            ++volcanicTimer; // 增加计时器
            return; // 未到触发时间则退出
        }

        // 获取施法者的生物对象 - 确保是有效的怪物单位
        auto caster = GetCaster()->ToCreature();
        // 安全检查：验证施法者有效性、词缀状态、AI控制状态和敌对状态
        if (!caster || caster->IsAffixDisabled(Affixes::Volcanic) || (caster->AI() && caster->AI()->IsInControl()) || !caster->IsHostileToPlayers())
            return;

        // 检查怪物的主人（如宠物、召唤物等） - 防止玩家宠物触发火山效果
        if (auto owner = caster->GetAnyOwner())
        {
            if (owner->IsPlayer()) // 如果主人是玩家则不触发火山效果
                return;

            // 如果主人是生物且不是首领，也不触发火山效果 - 只有首领的召唤物才能触发
            if (owner->IsCreature() && !owner->ToCreature()->IsDungeonBoss())
                return;
        }

        // 获取当前地图对象 - 用于遍历地图中的所有玩家
        auto _map = caster->GetMap();
        if (!_map) // 如果无法获取地图则退出
            return;

        // 获取地图中的所有玩家列表 - 火山效果需要对所有符合条件的玩家生效
        Map::PlayerList const& players = _map->GetPlayers();
        // 遍历地图中的每个玩家
        for (Map::PlayerList::const_iterator itr = players.begin(); itr != players.end(); ++itr)
        {
            if (auto player = itr->getSource()) // 获取玩家对象
            {
                // 检查玩家是否在战斗中且是有效的攻击目标
                if (player->isInCombat() && player->IsValidAttackTarget(caster))
                    // 距离检查：15-60码范围内的玩家 - 太近或太远都不触发火山效果
                    // 注释显示官方距离约为10米，这里使用15码作为最小距离
                    if (caster->GetDistance(player) > 15.0f && caster->GetDistance(player) < 60.0f)
                        // 在玩家位置施放火山地刺召唤法术 - 使用触发模式避免消耗法力和触发冷却
                        caster->CastSpell(player, ChallengerSummonVolcanicPlume, true);
            }
        }
    } // OnTick函数结束（火山词缀）

    // Necrotic - 死疽词缀触发函数
    // 当怪物攻击玩家时触发，为玩家叠加治疗吸收护盾
    void OnProc(AuraEffect const* /*auraEffect*/, ProcEventInfo& eventInfo)
    {
        // 检查是否在冷却时间内 - 防止死疽效果过于频繁触发
        if (necroticProcDelay)
            PreventDefaultAction(); // 阻止默认的触发动作 - 在冷却期间不应用死疽效果
        else
            necroticProcDelay = 1000; // 设置1秒的冷却时间 - 限制死疽效果的触发频率
    }

    // Fel Explosives - 爆裂词缀更新函数
    // 每帧调用，处理死疽冷却和邪能炸弹生成
    void OnUpdate(uint32 diff, AuraEffect* aurEff)
    {
        // 处理死疽词缀的冷却时间 - 确保死疽效果不会过于频繁触发
        if (necroticProcDelay)
        {
            if (necroticProcDelay <= diff) // 如果冷却时间已过
                necroticProcDelay = 0; // 重置冷却时间
            else
                necroticProcDelay -= diff; // 减少剩余冷却时间
        }

        // 检查爆裂词缀是否激活、效果索引是否正确、施法者是否在战斗中
        if (!aurEff->GetAmount() || aurEff->GetEffIndex() != EFFECT_12 || !GetCaster()->isInCombat())
            return;

        // 处理邪能炸弹生成计时器
        if (felExplosivesTimer <= diff) // 如果计时器时间已到
            felExplosivesTimer = urandms(8, 16); // 重置为新的随机时间间隔（8-16秒）
        else
        {
            felExplosivesTimer -= diff; // 减少剩余时间
            return; // 未到生成时间则退出
        }

        // 获取施法者的生物对象 - 确保是有效的怪物单位
        auto caster = GetCaster()->ToCreature();
        // 安全检查：验证施法者有效性、爆裂词缀状态、AI控制状态和敌对状态
        if (!caster || caster->IsAffixDisabled(Affixes::FelExplosives) || (caster->AI() && caster->AI()->IsInControl()) || !caster->IsHostileToPlayers())
            return;

        // 检查怪物的主人（如宠物、召唤物等） - 防止玩家宠物生成邪能炸弹
        if (auto owner = caster->GetAnyOwner())
        {
            if (owner->IsPlayer()) // 如果主人是玩家则不生成邪能炸弹
                return;

            // 如果主人是生物且不是首领，也不生成邪能炸弹 - 只有首领的召唤物才能生成
            if (owner->IsCreature() && !owner->ToCreature()->IsDungeonBoss())
                return;
        }

        // 获取当前地图对象 - 用于遍历地图中的所有玩家
        auto _map = caster->GetMap();
        if (!_map) // 如果无法获取地图则退出
            return;

        // 获取地图中的所有玩家列表 - 邪能炸弹需要检测玩家位置
        Map::PlayerList const& players = _map->GetPlayers();
        // 遍历地图中的每个玩家
        for (Map::PlayerList::const_iterator itr = players.begin(); itr != players.end(); ++itr)
        {
            if (auto player = itr->getSource()) // 获取玩家对象
            {
                // 检查玩家是否在战斗中且是有效的攻击目标
                if (player->isInCombat() && player->IsValidAttackTarget(caster))
                    // 距离检查：60码范围内的玩家 - 在有效范围内生成邪能炸弹
                    if (caster->GetDistance(player) < 60.0f)
                    {
                        // 在施法者位置生成邪能炸弹 - 使用远距离召唤法术
                        caster->CastSpell(caster, SPELL_FEL_EXPLOSIVES_SUMMON_2, true);
                        return; // 每次只生成一个邪能炸弹
                    }
            }
        }
    } // OnUpdate函数结束（爆裂词缀）

    // 注册光环脚本钩子函数 - 将各种处理函数绑定到对应的光环效果和事件
    void Register() override
    {
        // 注册效果数值计算钩子 - 在光环应用时计算各种效果的具体数值
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_0, SPELL_AURA_MOD_INCREASE_HEALTH_PERCENT); // 生命值百分比增加
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_1, SPELL_AURA_MOD_DAMAGE_PERCENT_DONE); // 伤害百分比增加
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_2, SPELL_AURA_DUMMY); // 狂怒词缀标记
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_3, SPELL_AURA_DUMMY); // 鼓舞词缀标记
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_4, SPELL_AURA_DUMMY); // 暴君词缀标记
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_7, SPELL_AURA_PERIODIC_DUMMY); // 火山词缀标记
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_8, SPELL_AURA_DUMMY); // 死疽词缀标记
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_9, SPELL_AURA_DUMMY); // 强韧词缀标记
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_10, SPELL_AURA_DUMMY); // 血池词缀标记
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_11, SPELL_AURA_DUMMY); // 震荡词缀标记
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_12, SPELL_AURA_DUMMY); // 爆裂词缀标记
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_might::CalculateAmount, EFFECT_13, SPELL_AURA_DUMMY); // 爆炸词缀标记
        // 注册周期性效果钩子 - 火山词缀每秒触发一次
        OnEffectPeriodic += AuraEffectPeriodicFn(spell_challengers_might::OnTick, EFFECT_7, SPELL_AURA_PERIODIC_DUMMY);
        // 注册触发效果钩子 - 死疽词缀在攻击时触发
        OnEffectProc += AuraEffectProcFn(spell_challengers_might::OnProc, EFFECT_8, SPELL_AURA_DUMMY);
        // 注册更新效果钩子 - 震荡和爆裂词缀需要持续更新计时器
        OnEffectUpdate += AuraEffectUpdateFn(spell_challengers_might::OnUpdate, EFFECT_11, SPELL_AURA_DUMMY); // 震荡词缀更新
        OnEffectUpdate += AuraEffectUpdateFn(spell_challengers_might::OnUpdate, EFFECT_12, SPELL_AURA_DUMMY); // 爆裂词缀更新
    }
}; // spell_challengers_might类结束

/// 206151 - Challenger's Burden - 挑战者负担光环脚本类
// 功能作用：为挑战模式中的玩家应用特定的词缀效果，主要处理对玩家不利的挑战机制
// 设计原理：与挑战者之力相对应，专门处理影响玩家的词缀效果（如溢出、胆怯、重伤）
// 技术优势：独立管理玩家词缀，避免与怪物词缀混淆，支持职业特定的效果判断
// 语义含义：代表挑战模式中玩家承受的额外负担和限制，增加游戏难度和策略性
// 底层机制：基于AuraScript框架，通过效果索引区分不同的玩家词缀类型
class spell_challengers_burden : public AuraScript
{
    PrepareAuraScript(spell_challengers_burden); // 光环脚本准备宏 - 初始化玩家词缀光环脚本

    // 计算玩家词缀效果数值 - 根据激活的词缀类型设置对应的效果强度
    void CalculateAmount(AuraEffect const* aurEff, float& amount, bool& /*canBeRecalculated*/)
    {
        auto caster = GetCaster(); // 获取光环施法者 - 通常是玩家自身
        if (!caster) // 安全检查：确保施法者存在
            return;

        auto player = caster->ToPlayer(); // 转换为玩家对象以访问玩家特有方法
        if (!player) // 确保施法者是玩家 - 挑战者负担只应用于玩家
            return;

        // 获取当前副本的场景进度信息 - 挑战模式基于场景系统实现
        auto progress = sScenarioMgr->GetScenario(caster->GetInstanceId());
        if (!progress) // 如果无法获取场景信息则退出
            return;

        // 获取挑战模式数据对象 - 包含当前挑战的词缀配置
        auto const& challenge = progress->GetChallenge();
        if (!challenge) // 如果无法获取挑战数据则退出
            return;

        // 根据光环效果索引设置对应的玩家词缀数值 - 每个索引对应不同的玩家负面效果
        switch (aurEff->GetEffIndex())
        {
            case EFFECT_1: // 效果索引1 - 溢出词缀标记（SPELL_AURA_DUMMY）
                // 溢出词缀：治疗溢出部分转化为吸收护盾，影响治疗策略
                amount = challenge->HasAffix(Affixes::Overflowing);
                break;
            case EFFECT_2: // 效果索引2 - 胆怯词缀标记（SPELL_AURA_MOD_THREAT）
                // 胆怯词缀：只对坦克职业生效，大幅降低仇恨生成，增加坦克难度
                amount = challenge->HasAffix(Affixes::Skittish) && player->isInTankSpec();
                break;
            case EFFECT_3: // 效果索引3 - 重伤词缀标记（SPELL_AURA_PERIODIC_DUMMY）
                // 重伤词缀：对所有玩家生效，生命值低于90%时持续受到伤害
                amount = challenge->HasAffix(Affixes::Grievous);
                break;
            default: // 默认情况 - 未定义的效果索引
                break;
        }
    } // CalculateAmount函数结束

    // Grievous - 重伤词缀周期性触发函数
    // 每秒检查玩家生命值，低于90%时应用重伤创口效果
    void OnTick(AuraEffect const* aurEff)
    {
        // 检查重伤词缀是否激活、光环拥有者是否存在且存活
        if (!aurEff->GetAmount() || !GetUnitOwner() || !GetUnitOwner()->isAlive())
            return;

        // 检查玩家生命值是否低于90% - 重伤词缀的触发条件
        if (GetUnitOwner()->HealthBelowPct(90))
        {
            // 如果玩家还没有重伤创口光环，则施加该效果
            if (!GetUnitOwner()->HasAura(ChallengerGrievousWound))
                // 对玩家自身施放重伤创口法术 - 造成持续伤害并可叠加
                GetUnitOwner()->CastSpell(GetUnitOwner(), ChallengerGrievousWound, true);
        }
        else
            // 如果生命值恢复到90%以上，移除重伤创口效果 - 鼓励玩家保持高生命值
            GetUnitOwner()->RemoveAurasDueToSpell(ChallengerGrievousWound);
    } // OnTick函数结束（重伤词缀）

    // 注册玩家词缀光环脚本钩子函数 - 将处理函数绑定到对应的玩家效果
    void Register() override
    {
        // 注册溢出词缀效果数值计算 - 虚拟效果，主要用于标记词缀激活状态
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_burden::CalculateAmount, EFFECT_1, SPELL_AURA_DUMMY);
        // 注册胆怯词缀仇恨修正计算 - 降低坦克的仇恨生成效率
        DoEffectCalcAmount += AuraEffectCalcAmountFn(spell_challengers_burden::CalculateAmount, EFFECT_2, SPELL_AURA_MOD_THREAT);
        // 注册重伤词缀周期性检查 - 每秒检查玩家生命值并应用重伤创口
        OnEffectPeriodic += AuraEffectPeriodicFn(spell_challengers_burden::OnTick, EFFECT_3, SPELL_AURA_PERIODIC_DUMMY);
    }
}; // spell_challengers_burden类结束

//240443 - Challenger's Burst - 挑战者爆炸光环脚本类
// 功能作用：实现爆炸词缀的伤害叠加机制，怪物死亡时对玩家造成可叠加的爆炸伤害
// 设计原理：通过光环层数计算伤害倍数，每个死亡的怪物增加一层爆炸效果
// 技术优势：自动计算伤害缩放，支持多怪物同时死亡的伤害累积
// 语义含义：代表挑战模式中怪物死亡时的报复性伤害，鼓励玩家分散击杀
// 底层机制：基于光环层数系统，通过周期性触发实现伤害计算和施放
class spell_challengers_burst : public AuraScript
{
    PrepareAuraScript(spell_challengers_burst); // 光环脚本准备宏 - 初始化爆炸词缀光环脚本

    // 爆炸伤害周期性触发函数 - 根据光环层数计算并造成爆炸伤害
    void OnTick(AuraEffect const* /*aurEff*/)
    {
        Unit* target = GetTarget(); // 获取光环目标 - 通常是受到爆炸影响的玩家
        if (!target) // 安全检查：确保目标存在
            return;

        // 获取爆炸伤害法术信息 - 法术ID 243237为实际的伤害法术
        if (SpellInfo const* spellInfo = sSpellMgr->GetSpellInfo(243237))
        {
            // 计算最终伤害 = 基础伤害 × 光环层数 - 每层代表一个死亡的怪物
            float bp = spellInfo->GetEffect(EFFECT_0)->BasePoints * target->GetAuraCount(GetId());
            // 对目标施放自定义伤害的爆炸法术 - 使用计算出的伤害值
            target->CastCustomSpell(target, 243237, &bp, NULL, NULL, true);
        }
    } // OnTick函数结束

    // 注册爆炸词缀光环脚本钩子函数
    void Register() override
    {
        // 注册周期性效果钩子 - 爆炸伤害的周期性触发
        OnEffectPeriodic += AuraEffectPeriodicFn(spell_challengers_burst::OnTick, EFFECT_0, SPELL_AURA_PERIODIC_DUMMY);
    }
}; // spell_challengers_burst类结束

//240559 - Challenger's Grievous Wound - 挑战者重伤创口光环脚本类
// 功能作用：实现重伤词缀的创口叠加机制，对生命值低于90%的玩家造成持续递增伤害
// 设计原理：通过光环层数系统实现伤害递增，每次触发增加一层创口效果
// 技术优势：自动叠加机制，无需外部控制，伤害随时间递增增加治疗压力
// 语义含义：代表重伤状态下伤口恶化的过程，鼓励玩家保持高生命值
// 底层机制：基于周期性百分比伤害光环，通过层数修改实现伤害递增
class spell_challengers_grievous_wound : public AuraScript
{
    PrepareAuraScript(spell_challengers_grievous_wound); // 光环脚本准备宏 - 初始化重伤创口光环脚本

    // 重伤创口周期性触发函数 - 每次触发增加一层创口效果
    void OnTick(AuraEffect const* aurEff)
    {
        // 获取光环基础对象并增加层数 - 每次触发叠加一层重伤创口
        if (auto aura = aurEff->GetBase())
            aura->ModStackAmount(1); // 增加光环层数，提高下次伤害
    } // OnTick函数结束

    // 注册重伤创口光环脚本钩子函数
    void Register() override
    {
        // 注册周期性百分比伤害效果钩子 - 重伤创口的持续伤害和层数叠加
        OnEffectPeriodic += AuraEffectPeriodicFn(spell_challengers_grievous_wound::OnTick, EFFECT_0, SPELL_AURA_PERIODIC_DAMAGE_PERCENT);
    }
}; // spell_challengers_grievous_wound类结束

// 209859 - Bolster - 挑战者鼓舞法术脚本类
// 功能作用：实现鼓舞词缀的目标过滤机制，确保只有符合条件的怪物获得鼓舞效果
// 设计原理：通过目标过滤器排除不应受到鼓舞的单位（玩家、首领、宠物等）
// 技术优势：精确的目标选择，避免鼓舞效果被错误应用，保持游戏平衡
// 语义含义：代表怪物死亡时对附近同伴的激励效果，增强剩余怪物的战斗力
// 底层机制：基于SpellScript法术脚本框架，通过目标选择钩子实现过滤逻辑
class spell_challengers_bolster : public SpellScript
{
    PrepareSpellScript(spell_challengers_bolster); // 法术脚本准备宏 - 初始化鼓舞法术脚本

    // 目标过滤函数 - 筛选出符合鼓舞条件的怪物单位
    void FilterTargets(std::list<WorldObject*>& targets)
    {
        if (targets.empty()) // 如果目标列表为空则直接返回
            return;

        std::list<WorldObject*> temp; // 临时列表存储过滤后的有效目标
        // 遍历所有潜在目标
        for (auto object : targets)
        {
            if (auto unit = object->ToUnit()) // 转换为单位对象
            {
                // 排除非战斗状态的单位和玩家 - 鼓舞只影响战斗中的怪物
                if (!unit->isInCombat() || unit->IsPlayer())
                    continue;

                // 排除玩家拥有的单位（宠物、召唤物等） - 防止玩家宠物获得鼓舞
                auto owner = unit->GetAnyOwner();
                if (owner && owner->IsPlayer())
                    continue;

                // 排除地下城首领 - 首领不受鼓舞词缀影响，避免过度强化
                if (Creature* creature = unit->ToCreature())
                    if (creature->IsDungeonBoss())
                        continue;

                temp.push_back(object); // 将符合条件的目标添加到临时列表
            }
        }
        targets = temp; // 用过滤后的列表替换原目标列表
    } // FilterTargets函数结束

    // 注册鼓舞法术脚本钩子函数
    void Register() override
    {
        // 为所有4个效果注册目标选择钩子 - 确保每个效果都使用相同的过滤逻辑
        OnObjectAreaTargetSelect += SpellObjectAreaTargetSelectFn(spell_challengers_bolster::FilterTargets, EFFECT_0, TARGET_UNIT_SRC_AREA_ALLY);
        OnObjectAreaTargetSelect += SpellObjectAreaTargetSelectFn(spell_challengers_bolster::FilterTargets, EFFECT_1, TARGET_UNIT_SRC_AREA_ALLY);
        OnObjectAreaTargetSelect += SpellObjectAreaTargetSelectFn(spell_challengers_bolster::FilterTargets, EFFECT_2, TARGET_UNIT_SRC_AREA_ALLY);
        OnObjectAreaTargetSelect += SpellObjectAreaTargetSelectFn(spell_challengers_bolster::FilterTargets, EFFECT_3, TARGET_UNIT_SRC_AREA_ALLY);
    }
}; // spell_challengers_bolster类结束

// 105877 - Volcanic Plume - 挑战者火山地刺NPC脚本类
// 功能作用：实现火山词缀的地刺效果，在玩家脚下生成造成伤害的火山地刺
// 设计原理：作为临时召唤物，生成后短暂延迟然后施放火山地刺法术造成范围伤害
// 技术优势：独立的AI脚本确保火山效果的可靠触发，不受其他系统干扰
// 语义含义：代表火山词缀中从地面喷发的岩浆地刺，为玩家提供视觉和伤害警告
// 底层机制：基于ScriptedAI框架，通过事件系统控制法术施放时机
struct npc_challenger_volcanic_plume : ScriptedAI
{
    // 构造函数 - 初始化火山地刺NPC的AI脚本
    npc_challenger_volcanic_plume(Creature* creature) : ScriptedAI(creature) {}

    EventMap events; // 事件管理器 - 控制火山地刺的施法时机

    // 重置函数 - 在NPC生成或重置时调用
    void Reset() override
    {
        events.Reset(); // 清空所有事件
        // 安排火山地刺施法事件 - 250毫秒后触发，给玩家短暂的反应时间
        events.ScheduleEvent(EVENT_1, 250);
    }

    // AI更新函数 - 每帧调用，处理事件和行为逻辑
    void UpdateAI(uint32 diff) override
    {
        events.Update(diff); // 更新事件计时器

        // 如果正在施法则等待施法完成 - 避免打断火山地刺的施放过程
        if (me->HasUnitState(UNIT_STATE_CASTING))
            return;

        // 处理所有到期的事件
        while (uint32 eventId = events.ExecuteEvent())
        {
            switch (eventId)
            {
                case EVENT_1: // 事件1 - 火山地刺施法事件
                    // 对自身位置施放火山地刺法术 - 造成范围伤害并提供视觉效果
                    me->CastSpell(me, ChallengerVolcanicPlume, false);
                    break;
                default: // 默认情况 - 未定义的事件
                    break;
            }
        }
    } // UpdateAI函数结束
}; // npc_challenger_volcanic_plume结构结束

// 120651 - Fel Explosives - 挑战者邪能炸弹NPC脚本类
// 功能作用：实现爆裂词缀的邪能炸弹机制，生成可被玩家攻击的炸弹单位
// 设计原理：作为图腾类单位，被动等待玩家攻击或自动爆炸，增加战术互动性
// 技术优势：图腾类型确保正确的目标选择和交互行为，定时爆炸机制增加紧迫感
// 语义含义：代表爆裂词缀中的邪能炸弹，需要玩家主动处理以避免大量伤害
// 底层机制：基于ScriptedAI和图腾系统，通过事件控制爆炸时机和视觉效果
struct npc_challenger_fel_explosives : ScriptedAI
{
    // 构造函数 - 初始化邪能炸弹NPC的AI脚本和属性设置
    npc_challenger_fel_explosives(Creature* creature) : ScriptedAI(creature)
    {
        // 设置为图腾类型 - 确保正确的目标选择行为和UI显示
        me->AddUnitTypeMask(UNIT_MASK_TOTEM);
        // 设置为被动反应状态 - 不会主动攻击，只等待玩家交互或定时爆炸
        me->SetReactState(REACT_PASSIVE);
    }

    EventMap events; // 事件管理器 - 控制邪能炸弹的爆炸时机

    // 重置函数 - 邪能炸弹不需要特殊的重置逻辑
    void Reset() override {}

    // 召唤完成回调函数 - 在邪能炸弹被召唤后立即调用
    void IsSummonedBy(Unit* summoner) override
    {
        // 立即施放视觉效果法术 - 为玩家提供明显的视觉提示
        DoCast(me, SPELL_FEL_EXPLOSIVES_VISUAL, true);
        // 安排爆炸事件 - 500毫秒后自动爆炸，给玩家处理时间
        events.ScheduleEvent(EVENT_1, 500);
    }

    // 法术施放完成回调函数 - 在法术施放结束后调用
    void SpellFinishCast(SpellInfo const* spellInfo) override
    {
        // 如果完成的是爆炸伤害法术，则销毁邪能炸弹
        if (spellInfo->Id == SPELL_FEL_EXPLOSIVES_DMG)
            me->DespawnOrUnsummon(100); // 100毫秒后销毁，确保伤害正确应用
    }

    // AI更新函数 - 每帧调用，处理爆炸事件
    void UpdateAI(uint32 diff) override
    {
        events.Update(diff); // 更新事件计时器

        // 如果正在施法则等待施法完成 - 避免打断爆炸过程
        if (me->HasUnitState(UNIT_STATE_CASTING))
            return;

        // 处理所有到期的事件
        while (uint32 eventId = events.ExecuteEvent())
        {
            switch (eventId)
            {
                case EVENT_1: // 事件1 - 邪能炸弹爆炸事件
                    // 施放爆炸伤害法术 - 对范围内的所有敌对目标造成伤害
                    DoCast(SPELL_FEL_EXPLOSIVES_DMG);
                    break;
            }
        }
    } // UpdateAI函数结束
}; // npc_challenger_fel_explosives结构结束

// 挑战钥匙物品脚本类 - 处理挑战模式钥匙的创建和初始化逻辑
// 功能作用：管理挑战模式钥匙的生成机制，区分新钥匙创建和现有钥匙初始化
// 设计原理：检查玩家是否已拥有钥匙，根据情况选择初始化或创建新钥匙
// 技术优势：自动化钥匙管理，确保玩家始终拥有有效的挑战钥匙
// 语义含义：代表挑战模式的准入凭证，记录玩家的挑战进度和等级
// 底层机制：基于ItemScript物品脚本框架，通过OnCreate钩子处理物品创建事件
class item_challenge_key : public ItemScript
{
public:
    // 构造函数 - 初始化挑战钥匙物品脚本，注册脚本名称
    item_challenge_key() : ItemScript("item_challenge_key") { }

    // 物品创建回调函数 - 在挑战钥匙物品被创建时调用
    // 参数说明：player-获得物品的玩家，item-被创建的物品对象
    // 返回值：false表示允许物品正常创建，true表示阻止创建
    bool OnCreate(Player* player, Item* item) override
    {
        // 检查玩家是否已经拥有挑战钥匙（物品ID 138019）
        if (player->HasItemCount(138019, 1, true))
            // 如果已有钥匙，则初始化新钥匙的属性 - 保持现有钥匙的等级和词缀
            player->InitChallengeKey(item);
        else
            // 如果没有钥匙，则创建全新的挑战钥匙 - 设置初始等级和随机词缀
            player->CreateChallengeKey(item);
        return false; // 返回false允许物品正常创建到玩家背包
    } // OnCreate函数结束
}; // item_challenge_key类结束

// 挑战模式脚本注册函数 - 将所有挑战模式相关的脚本注册到服务器系统
// 功能作用：统一注册所有挑战模式脚本，确保系统能够正确识别和调用这些脚本
// 设计原理：集中管理脚本注册，便于维护和调试挑战模式功能
// 技术优势：模块化注册机制，支持脚本的热加载和动态管理
// 语义含义：代表挑战模式系统的初始化入口，建立脚本与游戏逻辑的连接
// 底层机制：基于服务器脚本注册系统，通过宏和函数调用完成脚本绑定
void AddSC_challenge_scripts()
{
    // 注册挑战者之力光环脚本 - 处理怪物的词缀效果和属性缩放
    RegisterAuraScript(spell_challengers_might);
    // 注册挑战者负担光环脚本 - 处理玩家的词缀效果和限制
    RegisterAuraScript(spell_challengers_burden);
    // 注册挑战者爆炸光环脚本 - 处理爆炸词缀的伤害叠加机制
    RegisterAuraScript(spell_challengers_burst);
    // 注册挑战者重伤创口光环脚本 - 处理重伤词缀的创口叠加机制
    RegisterAuraScript(spell_challengers_grievous_wound);
    // 注册挑战者鼓舞法术脚本 - 处理鼓舞词缀的目标过滤逻辑
    RegisterSpellScript(spell_challengers_bolster);
    // 注册挑战者火山地刺NPC脚本 - 处理火山词缀的地刺生成和伤害
    RegisterCreatureAI(npc_challenger_volcanic_plume);
    // 注册挑战者邪能炸弹NPC脚本 - 处理爆裂词缀的炸弹生成和爆炸
    RegisterCreatureAI(npc_challenger_fel_explosives);
    // 创建挑战钥匙物品脚本实例 - 处理挑战钥匙的创建和管理逻辑
    new item_challenge_key();
} // AddSC_challenge_scripts函数结束
