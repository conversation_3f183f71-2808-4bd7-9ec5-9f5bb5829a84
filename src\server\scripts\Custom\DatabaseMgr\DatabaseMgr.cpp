﻿#include "DatabaseMgr.h"
#include "DatabaseEnv.h"
#include "../MainFunc/MainFunc.h"
#include "../Template/Req.h"
#include "../Template/Rew.h"
#include "../ItemMod/ItemUpgrade.h"
#include "../ItemMod/ItemRecovery.h"
#include "../ItemMod/ItemOnUse.h"
#include "../CCommand/CCommand.h"
#include "../SpellMod/SpellBaseManager.h"
#include "../UnitMod/CreatureMod.h"
#include "../Template/TipTexts.h"
#include "../UnitMod/VendorMod.h"
#include "../SpellMod/SpellMotion.h"
#include "../Template/Summon.h"
#include "../UnitMod/PlayerClassMod.h"
#include "../UnitMod/PlayerSpecMod.h"
#include "../MapMod/MapUnitControl.h"
#include "../Template/LinkGrave.h"
#include "../DisableStr/DisableStr.h"
#include "../ItemMod/ItemOnSell.h"
#include "../ItemMod/ItemOnBuy.h"
#include "../ItemMod/ItemOnEquip.h"
#include "../ItemMod/ItemOnExpire.h"
#include "../MapMod/MapItemBonus.h"
#include "../Template/BonusGroup.h"
#include "../ItemMod/ItemOnUpBonus.h"
#include "../Template/RandomSpell.h"
#include "../InstanceMod/MythicSetGObjLoot.h"
#include "../Template/UpRankGroup.h"
#include "../WorldChat/WorldChat.h"
#include "../Switch/Switch.h"
#include "../TriggerMgr/TriggerMgr.h"
#include "../BGMod/BattleGroundMod.h"
#include "../CustomEvent/ArenaDuel/ArenaDuel.h"
#include "../CustomEvent/Event.h"
#include "../ItemMod/ItemRefreshEnchant.h"
#include "../Template/EnchantGroup.h"
#include "../ItemMod/ItemCreateEnchant.h"
#include "../Template/ItemGroup.h"
#include "../ItemGUID/ItemGuidEffexts.h"
#include "../SpellMod/LootModFromSpell.h"
#include "AutoBuildItemTemplateData.h"
#include "../Template/SpellAuraPool.h"
#include "../StatPointsMod/StatPointsMod.h"
#include "../ItemMod/ItemInBags.h"
#include "../SpellMod/AuraMod.h"
#include "../ItemMod/ItemCombineEff.h"
#include "../VIP/VIP.h"
#pragma execution_character_set("utf-8")
DataMgr::DataMgr()
{
}

DataMgr::~DataMgr()
{
}

DataMgr* DataMgr::instance()
{
	static DataMgr instance;
	return &instance;
}

void DataMgr::LoadCustomDataBase(bool online)
{
	uint32 oldMSTime = getMSTime();

	if (online)
	{
		sDB2Manager.ClearCustomHotfix();	//每次在线加载前清空
		auto threadPool = new ThreadPoolMap();
		threadPool->start(std::max(std::thread::hardware_concurrency() / 2, 4u));
		std::map<uint32, std::string> & TableHashMaps = sWorld->GetTableHashMaps();
		for (std::map<uint32, std::string>::iterator itr = TableHashMaps.begin(); itr != TableHashMaps.end(); ++itr)
		{
			threadPool->schedule([=]() mutable
			{
				//TC_LOG_INFO(LOG_FILTER_GENERAL, ("【自定义数据库】重载TableHash: %u | TableName: %s"), itr->first, itr->second.c_str());
				auto storage = sDB2Manager.GetStorage(itr->first);
				if(storage)
					const_cast<DB2StorageBase*>(storage)->LoadFromCustomDB();
			});
		}

		if (threadPool)
			threadPool->wait();

		if (threadPool)
		{
			threadPool->stop();
			delete threadPool;
		}

		sDB2Manager.LoadDB2CustomStoresFromCustom();	//注意在DB2Manager::InitDB2CustomStores中的db2在如果在线加载后要实现更新还需要像这里进行加载一次，并且切记clear
		sCreatureMod->Load();	//暂不实时在线加载
		sSwitch->Load();		//需要在最开始就加载所以将在OnConfigLoad调用启动加载，再此调用在线加载
	}
	else
	{
		//初始化备份的sSpellCooldownsStore
		sSBMgr->InitCGSpellCooldownsStore();
	}

	sItemGroup->Load();
	sICEMod->Load();
	sEG->Load();
	sIRE->Load();
	sEvent->Load();
	sBGMod->Load();
	sTMgr->Load();
	sWC->Load();
	sUpBR->Load();
	sMGOBJL->Load();
	sRS->Load();
	sItemOnUpBonus->Load();
	sBonusGroup->Load();
	sMF->Load();
	sReq->Load();
	sRew->Load();
	sItemUp->Load();
	sItemRec->Load();
	sItemUse->Load();
	sCC->Load();
	sSBMgr->Load(online);
	sTT->Load();
	sVendorMod->Load(online);
	sSpellMotino->Load();
	sSum->Load();
	sPCMod->Load();
	sPSMod->Load();
	sMapCC->Load();
	sLGrave->Load();
	sDisStr->Load();
	sItemSell->Load();
	sItemBuy->Load();
	sItemEquip->Load();
	sItemExpire->Load();
	sMIB->Load();
	sItemGuidEffects->Load();
	sLootModSpell->Load();
	sABItem->LoadData();
	sSpellAuraPool->Load();
	sStatPointsMod->Load();
	sItemInBags->Load();
	sAuraMod->Load();
	sIComEff->Load();
	sVIP->Load();
	TC_LOG_INFO(LOG_FILTER_GENERAL, ("【自定义数据库】加载耗时：%u 毫秒"), GetMSTimeDiffToNow(oldMSTime));
}

void DataMgr::PlayerSaveCustomDB(Player* player)
{
	sStatPointsMod->SaveDB(player);
}

class DataMgrWorldScript : public WorldScript
{
public:
	DataMgrWorldScript() : WorldScript("DataMgrWorldScript") {}

	// run always when worldserver has loaded
	void OnStartup() override
	{
		sDataMgr->LoadCustomDataBase();
		sCreatureMod->Load();
		sArenaDuel->Load();
	}
};

void AddSC_DataMgr()
{
	new DataMgrWorldScript();
}