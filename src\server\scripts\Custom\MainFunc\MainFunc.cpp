﻿#include "MainFunc.h"
#include "DatabaseEnv.h"
#include "ScriptedGossip.h"
#include "../Template/Req.h"
#include "../ItemMod/ItemRecovery.h"
#include "../CCommand/CCommand.h"
#include "../Template/Rew.h"
#include "../CustomEvent/Event.h"
#include "../VIP/VIP.h"
#pragma execution_character_set("utf-8")

MainFunc::MainFunc()
{

}

MainFunc::~MainFunc()
{

}

MainFunc* MainFunc::instance()
{
	static MainFunc instance;
	return &instance;
}

void MainFunc::Load()
{
	MainFuncVec.clear();
	//																0				1		2		3		4			5		6				7			8	 9		10		 11			12			13			14	 15
	QueryResult result = WorldDatabase.PQuery(("SELECT 生物物品或物体ID, 菜单ID, 上级菜单ID,小图标ID, 大图标, 菜单文本, 联盟传送坐标ID, 部落传送坐标ID, 类型, 阵营, 功能选择, 弹窗文本, 需求模板ID, 奖励模板ID, GM命令组, 参数 FROM `【综合类】主要功能配置表`"));

	if (result)
	{
		uint32 count = 0;

		do
		{
			Field* fields = result->Fetch();
			MainFuncTemplate Temp;

			Temp.Entry = fields[0].GetUInt32();
			Temp.CurMenu = fields[1].GetUInt32();
			Temp.PreMenu = fields[2].GetUInt32();
			Temp.SmallIcon = fields[3].GetUInt8();
			Temp.BigIcon = fields[4].GetString();
			Temp.Text = fields[5].GetString();
			Temp.Pos_A = fields[6].GetUInt32();
			Temp.Pos_H = fields[7].GetUInt32();

			const char*  str = fields[8].GetCString();

			if (strcmp("Item", str) == 0)
				Temp.AgentType = MF_TYPE_ITEM;
			else if (strcmp("NPC", str) == 0)
				Temp.AgentType = MF_TYPE_CREATURE;
			else if (strcmp("GameObject", str) == 0)
				Temp.AgentType = MF_TYPE_GAMEOBJECT;
			else
				Temp.AgentType = MF_TYPE_NONE;


			str = fields[9].GetCString();

			if (strcmp(("联盟"), str) == 0)
				Temp.TeamId = TEAM_ALLIANCE;
			else if (strcmp(("部落"), str) == 0)
				Temp.TeamId = TEAM_HORDE;
			else
				Temp.TeamId = TEAM_NEUTRAL;


			str = fields[10].GetCString();

			if (strcmp(("返回主菜单"), str) == 0)
				Temp.FuncType = MF_MAIN_MENU;
			else if (strcmp(("返回上级菜单"), str) == 0)
				Temp.FuncType = MF_BACK_MENU;
			//else if (strcmp("角色修改名字", str) == 0)
			//	Temp.FuncType = MF_MOD_CHAR_NAME;
			//else if (strcmp("角色修改种族", str) == 0)
			//	Temp.FuncType = MF_MOD_CHAR_RACE;
			//else if (strcmp("角色修改阵营", str) == 0)
			//	Temp.FuncType = MF_MOD_CHAR_FACTION;
			//else if (strcmp("角色修改外观", str) == 0)
			//	Temp.FuncType = MF_MOD_CHAR_CUSTOMIZE;
			//else if (strcmp("角色武器技能全满", str) == 0)
			//	Temp.FuncType = MF_UPGRADE_WEAPON_SKILLS;
			//else if (strcmp("积分查询", str) == 0)
			//	Temp.FuncType = MF_QUERY_TOKEN;
			//else if (strcmp("脱离战斗", str) == 0)
			//	Temp.FuncType = MF_COMBATE_STOP;
			//else if (strcmp("泡点", str) == 0)
			//	Temp.FuncType = MF_ABTAIN_TIME_REWARD;
			//else if (strcmp("彩票", str) == 0)
			//	Temp.FuncType = MF_BUY_LOTTERY;
			//else if (strcmp("兑换码", str) == 0)
			//	Temp.FuncType = MF_CDK;
			else if (strcmp(("活动列表"), str) == 0)
				Temp.FuncType = MF_SHOW_ACTIVE_EVENTS;
			//else if (strcmp("军衔", str) == 0)
			//	Temp.FuncType = MF_UPGRADE_HR;
			//else if (strcmp("招募", str) == 0)
			//	Temp.FuncType = MF_RECRUIT;
			//else if (strcmp("任务传送", str) == 0)
			//	Temp.FuncType = MF_QUEST_TELE;
			else if (strcmp(("会员"), str) == 0)
				Temp.FuncType = MF_UPGRADE_VIP;
			//else if (strcmp("转生", str) == 0)
			//	Temp.FuncType = MF_REINCARNATION;
			//else if (strcmp("重置特定副本", str) == 0)
			//	Temp.FuncType = MF_RESET_INSTANCE;
			//else if (strcmp("自定义商业技能", str) == 0)
			//	Temp.FuncType = MF_CUSTOM_SKILL;
			//else if (strcmp("自定义等级", str) == 0)
			//	Temp.FuncType = MF_RANK;
			//else if (strcmp("自定义阵营", str) == 0)
			//	Temp.FuncType = MF_FACTION;
			//else if (strcmp("抽奖", str) == 0)
			//	Temp.FuncType = MF_LUCKDRAW;
			//else if (strcmp("绑定炉石点", str) == 0)
			//	Temp.FuncType = MF_HOME_BIND;
			//else if (strcmp("传送炉石点", str) == 0)
			//	Temp.FuncType = MF_HOME_TELE;
			//else if (strcmp("转职", str) == 0)
			//	Temp.FuncType = MF_ALT_CLASS;
						//else if (strcmp("法宝", str) == 0)
			//	Temp.FuncType = MF_TALISMAN;
			//else if (strcmp("斗气", str) == 0)
			//	Temp.FuncType = MF_STATPOINTS;
			//else if (strcmp("随机任务", str) == 0)
			//	Temp.FuncType = MF_RANDOM_QUEST;
			else if (strcmp(("角色重置天赋"), str) == 0)
				Temp.FuncType = MF_RESET_TALENTS;
			else if (strcmp(("重置日常任务"), str) == 0)
				Temp.FuncType = MF_RESET_DAILY_QUEST;
			else if (strcmp(("物品回收"), str) == 0)
				Temp.FuncType = MF_RECOVERY;
			else if (strcmp(("重置所有副本"), str) == 0)
				Temp.FuncType = MF_RESET_INSTANCE_ALL;
			else if (strcmp(("修理"), str) == 0)
				Temp.FuncType = MF_REPAIR;
			else if (strcmp(("银行"), str) == 0)
				Temp.FuncType = MF_BANK;
			else if (strcmp(("邮箱"), str) == 0)
				Temp.FuncType = MF_MAIL;
			else if (strcmp(("商人"), str) == 0)
				Temp.FuncType = MF_VENDOR;
			else
				Temp.FuncType = MF_NONE;

			Temp.PopText = fields[11].GetString();
			Temp.ReqId = fields[12].GetUInt32();
			Temp.RewId = fields[13].GetUInt32();

			Tokenizer commands(fields[14].GetString(), '#');
			for (Tokenizer::const_iterator itr = commands.begin(); itr != commands.end(); ++itr)
				Temp.Command.push_back(atoi(*itr));

			Temp.para = fields[15].GetString();

			MainFuncVec.push_back(Temp);

			++count;
		} while (result->NextRow());

		TC_LOG_INFO(LOG_FILTER_SERVER_LOADING, ("【综合类】主要功能配置表...加载【%u】条数据。"), count);
	} 


	PosMap.clear();
	result = WorldDatabase.PQuery(("SELECT 地图ID,X坐标,Y坐标,Z坐标,O坐标,坐标ID FROM `【模板类】坐标位置配置表`"));
	if (result)
	{
		uint32 count = 0;
		do
		{
			Field* fields = result->Fetch();
			PosTemplate Temp;
			Temp.map = fields[0].GetUInt32();
			Temp.x = fields[1].GetFloat();
			Temp.y = fields[2].GetFloat();
			Temp.z = fields[3].GetFloat();
			Temp.o = fields[4].GetFloat();
			uint32 ID = fields[5].GetUInt32();
			PosMap.insert(std::make_pair(ID, Temp));
			++count;
		} while (result->NextRow());

		TC_LOG_INFO(LOG_FILTER_SERVER_LOADING, ("【模板类】坐标位置配置表...加载【%u】条数据。"), count);
	}
	
}

void MainFunc::ClearMenu(Player* pl)
{
	pl->PlayerTalkClass->ClearMenus();
}

void MainFunc::CloseMenu(Player* pl)
{
	pl->CLOSE_GOSSIP_MENU();
}

void MainFunc::GetTelePos(uint32 posId, uint32 &map, float &x, float &y, float &z, float &o)
{
	std::unordered_map<uint32, PosTemplate>::iterator it = PosMap.find(posId);

	if (it != PosMap.end())
	{
		map = it->second.map;
		x = it->second.x;
		y = it->second.y;
		z = it->second.z;
		o = it->second.o;
	}
}

float MainFunc::GetDistance(Player* player, uint32 posId)
{
	std::unordered_map<uint32, PosTemplate>::iterator it = PosMap.find(posId);

	if (it != PosMap.end())
		if (it->second.map == player->GetMapId())
			return player->GetDistance(it->second.x, it->second.y, it->second.z);

	return 1000.0f;
}

bool MainFunc::IsPosId(uint32 id)
{
	std::unordered_map<uint32, PosTemplate>::iterator it = PosMap.find(id);

	return it != PosMap.end() ? true : false;
}

void MainFunc::Tele(Player* player, uint32 posId)
{
	if (posId == 0)
		return;

	std::unordered_map<uint32, PosTemplate>::iterator it = PosMap.find(posId);

	if (it != PosMap.end())
		player->TeleportTo(it->second.map, it->second.x, it->second.y, it->second.z, it->second.o);
}

void MainFunc::GetExtraData(Player* pl, Object* obj, uint32 CurMenu, uint32 PreMenu, uint32 &RewId, std::vector<uint32> &Command, uint32 &PosId, std::string& para, uint32 &ReqId, uint32 &FuncType)
{
	MFAgentTypes AgentType = GetAgentType(obj);
	uint32 Entry = obj->GetEntry();

	for (auto itr = MainFuncVec.begin(); itr != MainFuncVec.end(); itr++)
		if (AgentType == itr->AgentType && Entry == itr->Entry && itr->CurMenu == CurMenu && itr->PreMenu == PreMenu)
		{
			RewId = itr->RewId;
			para = itr->para;
			Command = itr->Command;
			ReqId = itr->ReqId;
			FuncType = itr->FuncType;
			if (pl->GetTeamId() == TEAM_ALLIANCE)
				PosId = itr->Pos_A;
			else
				PosId = itr->Pos_H;
			break;
		}
}

MFAgentTypes MainFunc::GetAgentType(Object* obj)
{
	switch (obj->GetTypeId())
	{
	case TYPEID_ITEM:
		return MF_TYPE_ITEM;
	case TYPEID_UNIT:
		return MF_TYPE_CREATURE;
	case TYPEID_GAMEOBJECT:
		return MF_TYPE_GAMEOBJECT;
	}

	return MF_TYPE_NONE;
}

uint32 MainFunc::GetPreMenu(Object* obj, uint32 CurMenu)
{
	MFAgentTypes AgentType = GetAgentType(obj);
	uint32 Entry = obj->GetEntry();

	for (auto itr = MainFuncVec.begin(); itr != MainFuncVec.end(); itr++)
		if (AgentType == itr->AgentType && Entry == itr->Entry && itr->CurMenu == CurMenu)
			return itr->PreMenu;

	return 0;
}

bool MainFunc::HasNextMenu(Object* obj, uint32 Menu)
{
	MFAgentTypes AgentType = GetAgentType(obj);
	uint32 Entry = obj->GetEntry();

	for (auto itr = MainFuncVec.begin(); itr != MainFuncVec.end(); itr++)
		if (AgentType == itr->AgentType && Entry == itr->Entry && itr->PreMenu == Menu)
			return true;

	return false;
}

bool MainFunc::CheckTeamId(Player*  pl, TeamId teamId)
{
	if (teamId == TEAM_NEUTRAL || pl->GetTeamId() == teamId)
		return true;

	return false;
}

void MainFunc::AddGossip(Player* pl, Object* obj, uint32 PreMenu)
{
	ClearMenu(pl);

	uint32 Entry = obj->GetEntry();
	MFAgentTypes AgentType = GetAgentType(obj);

	for (auto itr = MainFuncVec.begin(); itr != MainFuncVec.end(); itr++)
	{
		if (AgentType == itr->AgentType && Entry == itr->Entry && itr->PreMenu == PreMenu && CheckTeamId(pl, itr->TeamId))
		{
			uint32 ReqId = itr->ReqId;
			//uint32 sender = HL(itr->PreMenu, itr->CurMenu);
			//uint32 action = HL(itr->FuncType, ReqId);

			uint32 sender = SENDER_MAINFUNC_INFO + itr->PreMenu;
			uint32 action = ACTION_MAINFUNC_INFO + itr->CurMenu;

			std::string text = itr->BigIcon.empty() ? itr->Text : "|TInterface/ICONS/" + itr->BigIcon + ":30:30:0:0|t" + itr->Text;
			if (sReq->Pop(itr->ReqId))
				pl->ADD_GOSSIP_ITEM_EXTENDED(itr->SmallIcon, text, sender, action, sReq->Notice(pl, ReqId, itr->Text, ""), sReq->Golds(ReqId), false);
			else if (!itr->PopText.empty())
				pl->ADD_GOSSIP_ITEM_EXTENDED(itr->SmallIcon, text, sender, action, itr->PopText, 0, false);
			else
				pl->ADD_GOSSIP_ITEM(itr->SmallIcon, text, sender, action);
		}
	}

	if (obj->IsCreature())
		pl->SEND_GOSSIP_MENU(pl->GetGossipTextId(obj->ToCreature()), obj->GetGUID());
	else
		pl->SEND_GOSSIP_MENU(DEFAULT_GOSSIP_MESSAGE, obj->GetGUID());
}

void MainFunc::DoAction(Player* pl, Object* obj, uint32 sender, uint32 action)
{
	ClearMenu(pl);

	if (sender < SENDER_MAINFUNC_INFO || action < ACTION_MAINFUNC_INFO)
		return;

	uint32 PreMenu = sender - SENDER_MAINFUNC_INFO;
	uint32 CurMenu = action - ACTION_MAINFUNC_INFO;

	switch (PreMenu)
	{
	case SENDER_VIP_CURR:
		sVIP->AddGossip(pl, obj);
		return;
	case SENDER_VIP_UP:
		sVIP->Up(pl);
		CloseMenu(pl);
		return;
	default:
		break;
	}

	uint32 FuncType = 0;
	uint32 ReqId = 0;
	uint32 RewId = 0;
	std::vector<uint32> Command;
	uint32 PosId = 0;
	std::string para;
	GetExtraData(pl, obj, CurMenu, PreMenu, RewId, Command, PosId, para, ReqId, FuncType);

	//需求检测
	if (!sReq->Check(pl, ReqId))
	{
		//显示当前菜单
		AddGossip(pl, obj, PreMenu);
		return;
	}
	
	//如果消耗的是脚本本身物品，这里提前删除可能引起野指针，是否需要AddDelayedEvent来缓冲
	sReq->Des(pl, ReqId);


	sRew->Rews(pl, RewId);
	Tele(pl, PosId);

	//添加下级菜单
	if (HasNextMenu(obj, CurMenu))
		AddGossip(pl, obj, CurMenu);

	switch (FuncType)
	{
	//case MF_NONE:
	//	//若没有下级菜单，显示当前菜单
	//	if (!HasNextMenu(obj, CurMenu))
	//		AddGossip(pl, obj, PreMenu);
	//	break;
	case MF_MAIN_MENU:
		AddGossip(pl, obj, 0);
		break;
	case MF_RECOVERY:
		sItemRec->SendGossipHello(pl);
		//CloseMenu(pl);
		break;
	case MF_REPAIR:
		pl->DurabilityRepairAll(false, 0, false);
		ChatHandler(pl->GetSession()).SendSysMessage(("修理完成"));
		CloseMenu(pl);
		break;
	case MF_BANK:
		pl->GetSession()->SendShowBank(pl->GetGUID());
		CloseMenu(pl);
		break;
	case MF_MAIL:
		if (!pl->HasSpellCooldown(54710))
		{
			pl->CastSpell(pl, 54710, true);
			pl->AddSpellCooldown(54710, 0, 90 * IN_MILLISECONDS);
		}
		else
			pl->GetSession()->SendNotification(("邮箱功能冷却剩余时间%d秒"), pl->GetSpellCooldownDelay(54710) / IN_MILLISECONDS);
		CloseMenu(pl);
		break;
	case MF_UPGRADE_VIP:
		sVIP->AddGossip(pl, obj);
		break;
	case MF_RESET_DAILY_QUEST:
		pl->DailyReset();
		CloseMenu(pl);
		break;
	case MF_RESET_INSTANCE_ALL:
		sCC->DoCommand(pl, ".instance unbind all");
		CloseMenu(pl);
		break;
	case MF_RESET_TALENTS:
		sCC->DoCommand(pl, ".reset all talents");
		CloseMenu(pl);
		ChatHandler(pl->GetSession()).SendSysMessage(("已重置角色天赋"));
		break;
	case MF_BACK_MENU:
		//返回上级菜单
		AddGossip(pl, obj, GetPreMenu(obj, PreMenu));
		return;
	case SENDER_CUSTOM_EVENT_ACTIVE:
		sEvent->AcceptInvitation(pl, action + 1000000);
		CloseMenu(pl);
		return;
	case SENDER_CUSTOM_EVENT_DEACTIVE:
		sEvent->AddEventList(pl, obj);
		return;
	case MF_SHOW_ACTIVE_EVENTS:
		sEvent->AddEventList(pl, obj);
		break;
	case MF_VENDOR:
	{
		ObjectGuid guid = (obj && obj->GetTypeId() == TYPEID_UNIT) ? obj->GetGUID() : pl->GetGUID();
		pl->GetSession()->SendCommandListInventory(guid, atoi(para.c_str()));
	}
		break;
	default:
		break;
	}

	//执行命令组
	for (std::vector<uint32>::iterator itr = Command.begin(); itr != Command.end(); ++itr)
		sCC->DoCommandByID(pl, *itr);
}

class MainFunc_CreatureScript : public CreatureScript
{
public:
	MainFunc_CreatureScript() : CreatureScript("MainFunc_CreatureScript") { }


	bool OnGossipHello(Player* player, Creature* creature) override
	{
		sMF->AddGossip(player, creature, 0);
		return true;
	}

	bool OnGossipSelect(Player* player, Creature* creature, uint32 sender, uint32 action) override
	{
		sMF->DoAction(player, creature, sender, action);

		return true;
	}

};

class MainFunc_ItemScript : public ItemScript
{
public:
	MainFunc_ItemScript() : ItemScript("MainFunc_ItemScript") { }

	bool OnUse(Player* player, Item* item, SpellCastTargets const& targets) override
	{
		sMF->AddGossip(player, item, 0);
		return true;
	}

	void OnGossipSelect(Player* player, Item* item, uint32 sender, uint32 action) override
	{
		sMF->DoAction(player, item, sender, action);
	}

};

class MainFunc_GameObjectScript :public GameObjectScript
{
public:
	MainFunc_GameObjectScript() : GameObjectScript("MainFunc_GameObjectScript") { }

	bool OnGossipHello(Player* player, GameObject* go) override
	{
		sMF->AddGossip(player, go, 0);
		return true;
	}

	bool OnGossipSelect(Player* player, GameObject* go, uint32 sender, uint32 action) override
	{
		sMF->DoAction(player, go, sender, action);
		return true;
	}
};

void AddSC_MainFunc() 
{
	new MainFunc_ItemScript();
	new MainFunc_CreatureScript();
	new MainFunc_GameObjectScript();
}